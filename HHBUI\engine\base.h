#pragma once

// C++17标准库头文件
#include <thread>
#include <condition_variable>
#include <shared_mutex>
#include <map>
#include <unordered_map>
#include <queue>
#include <deque>
#include <list>
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>
#include <functional>
#include <type_traits>
#include <utility>
#include <algorithm>
#include <numeric>

// Windows头文件
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <windows.h>

namespace HHBUI
{
	/**
	 * @brief [线程安全] 渲染线程基类
	 * @details 提供线程生命周期管理，支持暂停/恢复/停止操作
	 * @note 使用RAII原则管理线程资源，确保异常安全
	 */
	class TOAPI UIRenderThread
	{
	public:
		UIRenderThread() noexcept;
		virtual ~UIRenderThread() noexcept;

		// 禁用拷贝构造和赋值
		UIRenderThread(const UIRenderThread&) = delete;
		UIRenderThread& operator=(const UIRenderThread&) = delete;

		// 支持移动语义
		UIRenderThread(UIRenderThread&& other) noexcept;
		UIRenderThread& operator=(UIRenderThread&& other) noexcept;

		/**
		 * @brief 启动渲染线程
		 * @param pause 是否以暂停状态启动
		 * @return 启动是否成功
		 */
		[[nodiscard]] virtual bool Start(bool pause = false) noexcept;

		/**
		 * @brief 暂停线程执行
		 */
		virtual void Pause() noexcept;

		/**
		 * @brief 恢复线程执行
		 */
		virtual void Resume() noexcept;

		/**
		 * @brief 停止线程并等待结束
		 */
		virtual void Stop() noexcept;

		/**
		 * @brief 检查线程是否正在运行
		 */
		[[nodiscard]] bool IsRunning() const noexcept;

		/**
		 * @brief 检查线程是否已暂停
		 */
		[[nodiscard]] bool IsPaused() const noexcept;

		/**
		 * @brief 获取线程ID
		 */
		[[nodiscard]] std::thread::id GetThreadId() const noexcept;

	protected:
		/**
		 * @brief 渲染线程主循环函数 - 子类必须实现
		 * @note 此函数在独立线程中执行，需要处理线程安全
		 */
		virtual void RenderThread() = 0;

		/**
		 * @brief 线程初始化回调 - 子类可重写
		 */
		virtual void OnThreadStart() {}

		/**
		 * @brief 线程结束回调 - 子类可重写
		 */
		virtual void OnThreadStop() {}

	private:
		void ThreadMain() noexcept;
		void CleanupThread() noexcept;

		std::unique_ptr<std::thread> m_thread;
		mutable std::mutex m_mutex;
		std::condition_variable m_condition;
		std::atomic<bool> m_pause{false};
		std::atomic<bool> m_stop{true};
		std::atomic<bool> m_running{false};
	};
	/**
	 * @brief [线程安全] FPS计数器和帧率限制器
	 * @details 提供精确的FPS计算和帧率限制功能，支持高精度时间测量
	 */
	class TOAPI UIFPSCounter
	{
	public:
		UIFPSCounter() noexcept;
		~UIFPSCounter() = default;

		// 禁用拷贝，允许移动
		UIFPSCounter(const UIFPSCounter&) = delete;
		UIFPSCounter& operator=(const UIFPSCounter&) = delete;
		UIFPSCounter(UIFPSCounter&&) = default;
		UIFPSCounter& operator=(UIFPSCounter&&) = default;

		/**
		 * @brief 计算当前FPS
		 * @return 当前帧率值
		 * @note 线程安全，可在任意线程调用
		 */
		[[nodiscard]] float CalcFPS() noexcept;

		/**
		 * @brief 设置最大FPS限制
		 * @param fps 目标帧率，-1表示无限制，0表示暂停渲染
		 * @note 支持动态调整，立即生效
		 */
		void SetMaxFPS(float fps) noexcept;

		/**
		 * @brief 获取当前FPS限制设置
		 * @return 当前FPS限制值，-1表示无限制
		 */
		[[nodiscard]] float GetMaxFPS() const noexcept;

		/**
		 * @brief 执行帧率限制
		 * @details 根据设定的最大FPS进行精确的时间控制
		 * @note 应在每帧渲染结束后调用
		 */
		void LimitFPS() noexcept;

		/**
		 * @brief 重置FPS统计
		 */
		void Reset() noexcept;

		/**
		 * @brief 获取平均FPS（最近N帧）
		 * @param frameCount 统计帧数，默认60帧
		 * @return 平均帧率
		 */
		[[nodiscard]] float GetAverageFPS(size_t frameCount = 60) const noexcept;

		/**
		 * @brief 获取帧时间（毫秒）
		 * @return 上一帧耗时（毫秒）
		 */
		[[nodiscard]] float GetFrameTime() const noexcept;

	private:
		void UpdateFrameHistory() noexcept;

		// 使用高精度时钟
		using Clock = std::chrono::high_resolution_clock;
		using TimePoint = Clock::time_point;
		using Duration = Clock::duration;

		mutable std::mutex m_mutex;

		// FPS计算相关
		std::atomic<float> m_currentFPS{0.0f};
		std::atomic<float> m_maxFPS{-1.0f};
		std::atomic<uint32_t> m_frameCount{0};

		TimePoint m_lastTime{Clock::now()};
		TimePoint m_frameStartTime{Clock::now()};
		TimePoint m_frameEndTime{Clock::now()};

		// 帧率限制相关
		Duration m_frameInterval{};
		TimePoint m_nextFrameTime{Clock::now()};

		// 统计历史数据
		std::queue<float> m_frameTimeHistory;
		static constexpr size_t MAX_HISTORY_SIZE = 120; // 2秒历史（60FPS）

		// 性能统计
		TimePoint m_lastSecondTime{Clock::now()};
		std::atomic<uint32_t> m_framesThisSecond{0};
	};
	/**
	 * @brief [线程安全] 高性能队列容器
	 * @tparam T 元素类型，支持移动语义以提高性能
	 * @details 基于std::deque实现的线程安全队列，支持FIFO和LIFO操作
	 */
	template <typename T>
	class TOAPI UIQueue
	{
	public:
		using value_type = T;
		using size_type = typename std::deque<T>::size_type;
		using reference = typename std::deque<T>::reference;
		using const_reference = typename std::deque<T>::const_reference;

		UIQueue() = default;
		~UIQueue() = default;

		// 禁用拷贝，允许移动
		UIQueue(const UIQueue&) = delete;
		UIQueue& operator=(const UIQueue&) = delete;
		UIQueue(UIQueue&&) = default;
		UIQueue& operator=(UIQueue&&) = default;

		/**
		 * @brief 检查队列是否为空
		 * @return true if empty, false otherwise
		 */
		[[nodiscard]] bool empty() const noexcept
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);
			return m_queue.empty();
		}

		/**
		 * @brief 获取队列大小
		 * @return 队列中元素数量
		 */
		[[nodiscard]] size_type size() const noexcept
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);
			return m_queue.size();
		}

		/**
		 * @brief 在队列前端插入元素（LIFO）
		 * @param item 要插入的元素
		 */
		void push_front(const T& item)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.push_front(item);
			m_condition.notify_one();
		}

		/**
		 * @brief 在队列前端插入元素（移动版本）
		 * @param item 要移动插入的元素
		 */
		void push_front(T&& item)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.push_front(std::move(item));
			m_condition.notify_one();
		}

		/**
		 * @brief 在队列后端插入元素（FIFO）
		 * @param item 要插入的元素
		 */
		void push_back(const T& item)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.push_back(item);
			m_condition.notify_one();
		}

		/**
		 * @brief 在队列后端插入元素（移动版本）
		 * @param item 要移动插入的元素
		 */
		void push_back(T&& item)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.push_back(std::move(item));
			m_condition.notify_one();
		}

		/**
		 * @brief 从队列前端取出元素
		 * @param item 输出参数，存储取出的元素
		 * @return true if successful, false if queue is empty
		 */
		[[nodiscard]] bool try_pop_front(T& item) noexcept
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			if (m_queue.empty())
				return false;

			item = std::move(m_queue.front());
			m_queue.pop_front();
			return true;
		}

		/**
		 * @brief 阻塞式从队列前端取出元素
		 * @param item 输出参数，存储取出的元素
		 * @param timeout_ms 超时时间（毫秒），0表示无限等待
		 * @return true if successful, false if timeout
		 */
		[[nodiscard]] bool wait_and_pop_front(T& item, uint32_t timeout_ms = 0)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);

			if (timeout_ms == 0)
			{
				m_condition.wait(lock, [this] { return !m_queue.empty(); });
			}
			else
			{
				if (!m_condition.wait_for(lock, std::chrono::milliseconds(timeout_ms),
					[this] { return !m_queue.empty(); }))
				{
					return false; // 超时
				}
			}

			item = std::move(m_queue.front());
			m_queue.pop_front();
			return true;
		}

		/**
		 * @brief 清空队列
		 */
		void clear() noexcept
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.clear();
		}

		/**
		 * @brief 就地构造元素到队列后端
		 * @tparam Args 构造参数类型
		 * @param args 构造参数
		 */
		template<typename... Args>
		void emplace_back(Args&&... args)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.emplace_back(std::forward<Args>(args)...);
			m_condition.notify_one();
		}

		/**
		 * @brief 就地构造元素到队列前端
		 * @tparam Args 构造参数类型
		 * @param args 构造参数
		 */
		template<typename... Args>
		void emplace_front(Args&&... args)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.emplace_front(std::forward<Args>(args)...);
			m_condition.notify_one();
		}

		// 兼容性接口（保持向后兼容）
		void insertquque(T& t) { push_front(t); }
		void enqueue(T& t) { push_back(t); }
		bool dequeue(T& t) { return try_pop_front(t); }

	private:
		mutable std::shared_mutex m_mutex;
		std::condition_variable_any m_condition;
		std::deque<T> m_queue;
	};
	/**
	 * @brief UI对象基类
	 * @details 所有UI元素的基础类，提供基本的对象管理和层次结构支持
	 * @note 使用智能指针管理内存，确保异常安全和自动清理
	 */
	class TOAPI UIBase
	{
	public:
		UIBase() noexcept = default;
		virtual ~UIBase() noexcept = default;

		// 禁用拷贝，允许移动
		UIBase(const UIBase&) = delete;
		UIBase& operator=(const UIBase&) = delete;
		UIBase(UIBase&&) = default;
		UIBase& operator=(UIBase&&) = default;

		/**
		 * @brief 获取UI视图对象
		 * @return UI视图指针
		 */
		[[nodiscard]] LPVOID GetUIView() const noexcept { return m_UIView; }

		/**
		 * @brief 设置UI视图对象
		 * @param view UI视图指针
		 */
		void SetUIView(LPVOID view) noexcept { m_UIView = view; }

		/**
		 * @brief 获取UI窗口对象
		 * @return UI窗口指针
		 */
		[[nodiscard]] LPVOID GetUIWindow() const noexcept { return m_UIWindow; }

		/**
		 * @brief 设置UI窗口对象
		 * @param window UI窗口指针
		 */
		void SetUIWindow(LPVOID window) noexcept { m_UIWindow = window; }

		/**
		 * @brief 检查对象是否有效
		 * @return true if valid, false otherwise
		 */
		[[nodiscard]] virtual bool IsValid() const noexcept
		{
			return m_UIView != nullptr;
		}

		/**
		 * @brief 获取对象类型名称
		 * @return 类型名称字符串
		 */
		[[nodiscard]] virtual const wchar_t* GetTypeName() const noexcept
		{
			return L"UIBase";
		}

	protected:
		/**
		 * @brief 获取第一个子对象
		 * @return 子对象指针
		 */
		[[nodiscard]] LPVOID GetFirstChild() const noexcept { return m_objChildFirst; }

		/**
		 * @brief 设置第一个子对象
		 * @param child 子对象指针
		 */
		void SetFirstChild(LPVOID child) noexcept { m_objChildFirst = child; }

		/**
		 * @brief 获取最后一个子对象
		 * @return 子对象指针
		 */
		[[nodiscard]] LPVOID GetLastChild() const noexcept { return m_objChildLast; }

		/**
		 * @brief 设置最后一个子对象
		 * @param child 子对象指针
		 */
		void SetLastChild(LPVOID child) noexcept { m_objChildLast = child; }

		// UI对象数据成员
		LPVOID m_UIView = nullptr;
		LPVOID m_UIWindow = nullptr;

	private:
		// 子对象管理
		LPVOID m_objChildFirst = nullptr;
		LPVOID m_objChildLast = nullptr;

		// 友元类声明
		friend class UIWnd;
		friend class UIControl;
		friend class UICanvas;
		friend class UILayout;
		friend class UIAnimation;
	};

	/**
	 * @brief 定时器信息结构
	 * @details 用于管理UI对象的定时器，支持Windows定时器和高精度定时器
	 */
	struct TimerInfo
	{
		UIBase* pPropObj = nullptr;          ///< 关联的UI对象
		size_t nLocalID = 0;                 ///< 本地定时器ID
		HWND hWnd = nullptr;                 ///< 窗口句柄
		UINT uWinTimer = 0;                  ///< Windows定时器ID
		std::atomic<bool> bKilled{false};    ///< 是否已被销毁
		std::chrono::milliseconds interval{0}; ///< 定时器间隔
		std::chrono::steady_clock::time_point lastTrigger; ///< 上次触发时间

		TimerInfo() noexcept : lastTrigger(std::chrono::steady_clock::now()) {}

		TimerInfo(UIBase* obj, size_t id, HWND wnd, UINT timer, std::chrono::milliseconds intv) noexcept
			: pPropObj(obj), nLocalID(id), hWnd(wnd), uWinTimer(timer), interval(intv)
			, lastTrigger(std::chrono::steady_clock::now()) {}

		// 支持移动语义
		TimerInfo(TimerInfo&&) = default;
		TimerInfo& operator=(TimerInfo&&) = default;

		// 禁用拷贝
		TimerInfo(const TimerInfo&) = delete;
		TimerInfo& operator=(const TimerInfo&) = delete;

		/**
		 * @brief 检查定时器是否应该触发
		 * @return true if should trigger, false otherwise
		 */
		[[nodiscard]] bool ShouldTrigger() const noexcept
		{
			if (bKilled.load()) return false;
			auto now = std::chrono::steady_clock::now();
			return (now - lastTrigger) >= interval;
		}

		/**
		 * @brief 更新最后触发时间
		 */
		void UpdateTriggerTime() noexcept
		{
			lastTrigger = std::chrono::steady_clock::now();
		}
	};

	/// 定时器信息容器类型
	using VecTimerInfo = std::vector<std::unique_ptr<TimerInfo>>;

	/**
	 * @brief 定时器管理器
	 * @details 提供高精度定时器管理，支持批量操作和自动清理
	 */
	class TOAPI UITimerManager
	{
	public:
		UITimerManager() = default;
		~UITimerManager() = default;

		// 禁用拷贝和移动
		UITimerManager(const UITimerManager&) = delete;
		UITimerManager& operator=(const UITimerManager&) = delete;
		UITimerManager(UITimerManager&&) = delete;
		UITimerManager& operator=(UITimerManager&&) = delete;

		/**
		 * @brief 添加定时器
		 * @param obj 关联的UI对象
		 * @param interval 定时器间隔（毫秒）
		 * @param hWnd 窗口句柄
		 * @return 定时器ID，0表示失败
		 */
		[[nodiscard]] size_t AddTimer(UIBase* obj, std::chrono::milliseconds interval, HWND hWnd = nullptr) noexcept;

		/**
		 * @brief 移除定时器
		 * @param timerId 定时器ID
		 * @return true if successful, false otherwise
		 */
		bool RemoveTimer(size_t timerId) noexcept;

		/**
		 * @brief 移除对象的所有定时器
		 * @param obj UI对象指针
		 * @return 移除的定时器数量
		 */
		size_t RemoveObjectTimers(UIBase* obj) noexcept;

		/**
		 * @brief 处理定时器事件
		 * @details 检查所有定时器并触发到期的定时器
		 */
		void ProcessTimers() noexcept;

		/**
		 * @brief 清理所有定时器
		 */
		void Clear() noexcept;

		/**
		 * @brief 获取活跃定时器数量
		 * @return 定时器数量
		 */
		[[nodiscard]] size_t GetActiveTimerCount() const noexcept;

	private:
		mutable std::shared_mutex m_mutex;
		VecTimerInfo m_timers;
		std::atomic<size_t> m_nextId{1};
	};

	/**
	 * @brief 全局定时器管理器实例
	 * @details 提供全局访问的定时器管理功能
	 */
	extern UITimerManager& GetGlobalTimerManager() noexcept;

} // namespace HHBUI