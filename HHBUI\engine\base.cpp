﻿#include "pch.h"
#include "base.h"
#include <algorithm>
#include <numeric>

namespace HHBUI
{
	// ==================== UIRenderThread Implementation ====================

	UIRenderThread::UIRenderThread() noexcept
		: m_thread(nullptr)
		, m_pause(false)
		, m_stop(true)
		, m_running(false)
	{
	}

	UIRenderThread::~UIRenderThread() noexcept
	{
		Stop();
	}

	UIRenderThread::UIRenderThread(UIRenderThread&& other) noexcept
		: m_thread(std::move(other.m_thread))
		, m_pause(other.m_pause.load())
		, m_stop(other.m_stop.load())
		, m_running(other.m_running.load())
	{
		other.m_pause = false;
		other.m_stop = true;
		other.m_running = false;
	}

	UIRenderThread& UIRenderThread::operator=(UIRenderThread&& other) noexcept
	{
		if (this != &other)
		{
			Stop(); // 停止当前线程

			m_thread = std::move(other.m_thread);
			m_pause = other.m_pause.load();
			m_stop = other.m_stop.load();
			m_running = other.m_running.load();

			other.m_pause = false;
			other.m_stop = true;
			other.m_running = false;
		}
		return *this;
	}

	bool UIRenderThread::Start(bool pause) noexcept
	{
		try
		{
			if (m_stop.load())
			{
				m_pause = pause;
				m_stop = false;
				m_running = true;
				m_thread = std::make_unique<std::thread>(&UIRenderThread::ThreadMain, this);
				return true;
			}
		}
		catch (...)
		{
			m_stop = true;
			m_running = false;
		}
		return false;
	}

	void UIRenderThread::Pause() noexcept
	{
		m_pause = true;
	}

	void UIRenderThread::Resume() noexcept
	{
		m_pause = false;
		m_condition.notify_one();
	}

	void UIRenderThread::Stop() noexcept
	{
		if (m_thread && m_thread->joinable())
		{
			m_stop = true;
			m_pause = false;
			m_condition.notify_all();

			try
			{
				m_thread->join();
			}
			catch (...)
			{
				// 忽略join异常
			}

			CleanupThread();
		}
	}

	bool UIRenderThread::IsRunning() const noexcept
	{
		return m_running.load() && !m_stop.load();
	}

	bool UIRenderThread::IsPaused() const noexcept
	{
		return m_pause.load();
	}

	std::thread::id UIRenderThread::GetThreadId() const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);
		return m_thread ? m_thread->get_id() : std::thread::id{};
	}

	void UIRenderThread::ThreadMain() noexcept
	{
		try
		{
			OnThreadStart();

			while (!m_stop.load())
			{
				// 处理暂停状态
				if (m_pause.load())
				{
					std::unique_lock<std::mutex> lock(m_mutex);
					m_condition.wait(lock, [this] { return !m_pause.load() || m_stop.load(); });
				}

				if (!m_stop.load())
				{
					RenderThread();
				}
			}

			OnThreadStop();
		}
		catch (...)
		{
			// 线程异常处理
			m_stop = true;
		}

		m_running = false;
	}

	void UIRenderThread::CleanupThread() noexcept
	{
		m_thread.reset();
		m_running = false;
	}

	// ==================== UIFPSCounter Implementation ====================

	UIFPSCounter::UIFPSCounter() noexcept
		: m_currentFPS(0.0f)
		, m_maxFPS(-1.0f)
		, m_frameCount(0)
		, m_lastTime(Clock::now())
		, m_frameStartTime(Clock::now())
		, m_frameEndTime(Clock::now())
		, m_frameInterval{}
		, m_nextFrameTime(Clock::now())
		, m_lastSecondTime(Clock::now())
		, m_framesThisSecond(0)
	{
	}

	float UIFPSCounter::CalcFPS() noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		const auto currentTime = Clock::now();
		const auto duration = std::chrono::duration_cast<std::chrono::microseconds>(currentTime - m_lastTime);

		// 更新帧计数
		++m_frameCount;
		++m_framesThisSecond;

		// 记录帧时间
		UpdateFrameHistory();

		// 每秒计算一次FPS
		constexpr auto oneSecond = std::chrono::seconds(1);
		if (duration >= oneSecond)
		{
			const double durationSeconds = duration.count() / 1000000.0;
			m_currentFPS = static_cast<float>(m_frameCount.load()) / static_cast<float>(durationSeconds);

			m_frameCount = 0;
			m_lastTime = currentTime;
		}

		return m_currentFPS.load();
	}
	void UIFPSCounter::SetMaxFPS(float fps) noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		if (fps < 0.0f)
		{
			m_maxFPS = -1.0f; // 无限制
			m_frameInterval = Duration::zero();
		}
		else if (fps == 0.0f)
		{
			m_maxFPS = 0.0f; // 暂停渲染
			m_frameInterval = Duration::max();
		}
		else
		{
			m_maxFPS = fps;
			// 计算帧间隔时间
			const auto intervalMs = static_cast<int64_t>(1000.0 / fps);
			m_frameInterval = std::chrono::milliseconds(intervalMs);
		}

		// 重置下一帧时间
		m_nextFrameTime = Clock::now() + m_frameInterval;
	}

	float UIFPSCounter::GetMaxFPS() const noexcept
	{
		return m_maxFPS.load();
	}

	void UIFPSCounter::LimitFPS() noexcept
	{
		const float maxFps = m_maxFPS.load();

		if (maxFps == 0.0f)
		{
			// 暂停渲染，等待较长时间
			std::this_thread::sleep_for(std::chrono::milliseconds(16));
			return;
		}

		if (maxFps > 0.0f)
		{
			std::lock_guard<std::mutex> lock(m_mutex);

			const auto now = Clock::now();
			if (now < m_nextFrameTime)
			{
				// 精确等待到下一帧时间
				std::this_thread::sleep_until(m_nextFrameTime);
			}

			// 更新下一帧时间
			m_nextFrameTime = std::max(m_nextFrameTime + m_frameInterval, now);
		}
		// maxFps < 0 表示无限制，不进行等待
	}

	void UIFPSCounter::Reset() noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		m_currentFPS = 0.0f;
		m_frameCount = 0;
		m_framesThisSecond = 0;

		const auto now = Clock::now();
		m_lastTime = now;
		m_frameStartTime = now;
		m_frameEndTime = now;
		m_lastSecondTime = now;
		m_nextFrameTime = now;

		// 清空历史数据
		while (!m_frameTimeHistory.empty())
		{
			m_frameTimeHistory.pop();
		}
	}

	float UIFPSCounter::GetAverageFPS(size_t frameCount) const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		if (m_frameTimeHistory.empty())
			return 0.0f;

		const size_t sampleCount = std::min(frameCount, m_frameTimeHistory.size());
		if (sampleCount == 0)
			return 0.0f;

		// 计算最近N帧的平均帧时间
		std::queue<float> tempQueue = m_frameTimeHistory;
		float totalTime = 0.0f;
		size_t count = 0;

		while (!tempQueue.empty() && count < sampleCount)
		{
			totalTime += tempQueue.front();
			tempQueue.pop();
			++count;
		}

		if (count == 0 || totalTime <= 0.0f)
			return 0.0f;

		const float avgFrameTime = totalTime / static_cast<float>(count);
		return 1000.0f / avgFrameTime; // 转换为FPS
	}

	float UIFPSCounter::GetFrameTime() const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		if (m_frameTimeHistory.empty())
			return 0.0f;

		return m_frameTimeHistory.back();
	}

	void UIFPSCounter::UpdateFrameHistory() noexcept
	{
		const auto now = Clock::now();
		const auto frameTime = std::chrono::duration_cast<std::chrono::microseconds>(now - m_frameStartTime);
		const float frameTimeMs = frameTime.count() / 1000.0f;

		m_frameTimeHistory.push(frameTimeMs);

		// 限制历史数据大小
		if (m_frameTimeHistory.size() > MAX_HISTORY_SIZE)
		{
			m_frameTimeHistory.pop();
		}

		m_frameStartTime = now;
	}

	// ==================== UITimerManager Implementation ====================

	size_t UITimerManager::AddTimer(UIBase* obj, std::chrono::milliseconds interval, HWND hWnd) noexcept
	{
		if (!obj || interval.count() <= 0)
			return 0;

		try
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);

			const size_t timerId = m_nextId.fetch_add(1);
			auto timer = std::make_unique<TimerInfo>(obj, timerId, hWnd, 0, interval);

			m_timers.push_back(std::move(timer));
			return timerId;
		}
		catch (...)
		{
			return 0;
		}
	}

	bool UITimerManager::RemoveTimer(size_t timerId) noexcept
	{
		if (timerId == 0)
			return false;

		try
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);

			auto it = std::find_if(m_timers.begin(), m_timers.end(),
				[timerId](const std::unique_ptr<TimerInfo>& timer) {
					return timer && timer->nLocalID == timerId;
				});

			if (it != m_timers.end())
			{
				(*it)->bKilled = true;
				m_timers.erase(it);
				return true;
			}
		}
		catch (...)
		{
			// 忽略异常
		}

		return false;
	}

	size_t UITimerManager::RemoveObjectTimers(UIBase* obj) noexcept
	{
		if (!obj)
			return 0;

		try
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);

			size_t removedCount = 0;
			auto it = m_timers.begin();

			while (it != m_timers.end())
			{
				if (*it && (*it)->pPropObj == obj)
				{
					(*it)->bKilled = true;
					it = m_timers.erase(it);
					++removedCount;
				}
				else
				{
					++it;
				}
			}

			return removedCount;
		}
		catch (...)
		{
			return 0;
		}
	}

	void UITimerManager::ProcessTimers() noexcept
	{
		try
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);

			for (auto& timer : m_timers)
			{
				if (timer && !timer->bKilled.load() && timer->ShouldTrigger())
				{
					timer->UpdateTriggerTime();

					// 这里可以添加定时器回调处理
					// 例如：发送Windows消息或调用回调函数
					if (timer->hWnd && IsWindow(timer->hWnd))
					{
						PostMessage(timer->hWnd, WM_TIMER, timer->uWinTimer, 0);
					}
				}
			}
		}
		catch (...)
		{
			// 忽略异常
		}
	}

	void UITimerManager::Clear() noexcept
	{
		try
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);

			for (auto& timer : m_timers)
			{
				if (timer)
				{
					timer->bKilled = true;
				}
			}

			m_timers.clear();
		}
		catch (...)
		{
			// 忽略异常
		}
	}

	size_t UITimerManager::GetActiveTimerCount() const noexcept
	{
		try
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);

			return std::count_if(m_timers.begin(), m_timers.end(),
				[](const std::unique_ptr<TimerInfo>& timer) {
					return timer && !timer->bKilled.load();
				});
		}
		catch (...)
		{
			return 0;
		}
	}

	// ==================== Global Timer Manager ====================

	UITimerManager& GetGlobalTimerManager() noexcept
	{
		static UITimerManager instance;
		return instance;
	}

} // namespace HHBUI
